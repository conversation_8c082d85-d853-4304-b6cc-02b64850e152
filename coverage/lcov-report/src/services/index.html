
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">26.75% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>107/400</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">20.7% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>41/198</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">28.33% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>17/60</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">28.96% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>104/359</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="DataIngestionService.js"><a href="DataIngestionService.js.html">DataIngestionService.js</a></td>
	<td data-value="39.32" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 39%"></div><div class="cover-empty" style="width: 61%"></div></div>
	</td>
	<td data-value="39.32" class="pct low">39.32%</td>
	<td data-value="89" class="abs low">35/89</td>
	<td data-value="29.26" class="pct low">29.26%</td>
	<td data-value="41" class="abs low">12/41</td>
	<td data-value="30.76" class="pct low">30.76%</td>
	<td data-value="13" class="abs low">4/13</td>
	<td data-value="39.53" class="pct low">39.53%</td>
	<td data-value="86" class="abs low">34/86</td>
	</tr>

<tr>
	<td class="file medium" data-value="PreprocessingService.js"><a href="PreprocessingService.js.html">PreprocessingService.js</a></td>
	<td data-value="75.4" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.4" class="pct medium">75.4%</td>
	<td data-value="61" class="abs medium">46/61</td>
	<td data-value="69.04" class="pct medium">69.04%</td>
	<td data-value="42" class="abs medium">29/42</td>
	<td data-value="90.9" class="pct high">90.9%</td>
	<td data-value="11" class="abs high">10/11</td>
	<td data-value="83.01" class="pct high">83.01%</td>
	<td data-value="53" class="abs high">44/53</td>
	</tr>

<tr>
	<td class="file low" data-value="SimilarityService.js"><a href="SimilarityService.js.html">SimilarityService.js</a></td>
	<td data-value="19.76" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 19%"></div><div class="cover-empty" style="width: 81%"></div></div>
	</td>
	<td data-value="19.76" class="pct low">19.76%</td>
	<td data-value="86" class="abs low">17/86</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="51" class="abs low">0/51</td>
	<td data-value="14.28" class="pct low">14.28%</td>
	<td data-value="14" class="abs low">2/14</td>
	<td data-value="22.66" class="pct low">22.66%</td>
	<td data-value="75" class="abs low">17/75</td>
	</tr>

<tr>
	<td class="file low" data-value="UnificationService.js"><a href="UnificationService.js.html">UnificationService.js</a></td>
	<td data-value="5.48" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.48" class="pct low">5.48%</td>
	<td data-value="164" class="abs low">9/164</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="64" class="abs low">0/64</td>
	<td data-value="4.54" class="pct low">4.54%</td>
	<td data-value="22" class="abs low">1/22</td>
	<td data-value="6.2" class="pct low">6.2%</td>
	<td data-value="145" class="abs low">9/145</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-29T08:41:53.598Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    