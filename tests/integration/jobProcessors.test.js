// Mock fs module first, before any other modules are loaded
jest.mock('fs', () => ({
  existsSync: jest.fn().mockReturnValue(true),
  mkdirSync: jest.fn(),
  readFileSync: jest.fn(),
  writeFileSync: jest.fn(),
  unlinkSync: jest.fn()
}));

const mongoose = require('mongoose');
const Record = require('../../src/models/Record');
const UnifiedEntity = require('../../src/models/UnifiedEntity');

const dataIngestionProcessors = require('../../src/jobs/processors/dataIngestionProcessors');
const preprocessingProcessors = require('../../src/jobs/processors/preprocessingProcessors');
const similarityProcessors = require('../../src/jobs/processors/similarityProcessors');
const unificationProcessors = require('../../src/jobs/processors/unificationProcessors');
const batchProcessors = require('../../src/jobs/processors/batchProcessors');
const cleanupProcessors = require('../../src/jobs/processors/cleanupProcessors');

// Mock database connection
jest.mock('mongoose', () => {
  const mockMongoose = {
    connect: jest.fn().mockResolvedValue(true),
    disconnect: jest.fn().mockResolvedValue(true),
    Schema: jest.fn().mockImplementation(() => ({
      index: jest.fn(),
      pre: jest.fn(),
      methods: {},
      statics: {}
    })),
    model: jest.fn().mockReturnValue({
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn()
    })
  };

  // Add Schema.Types to the mock
  mockMongoose.Schema.Types = {
    ObjectId: jest.fn(),
    Mixed: jest.fn()
  };

  return mockMongoose;
});

// Mock models
jest.mock('../../src/models/Record');
jest.mock('../../src/models/UnifiedEntity');

// Mock services
jest.mock('../../src/services/DataIngestionService');
jest.mock('../../src/services/PreprocessingService');
jest.mock('../../src/services/SimilarityService');
jest.mock('../../src/services/UnificationService');

// Mock file system for cleanup tests
jest.mock('fs', () => ({
  promises: {
    access: jest.fn().mockResolvedValue(true),
    readdir: jest.fn().mockResolvedValue([
      { name: 'old-file.txt', isFile: () => true },
      { name: 'recent-file.txt', isFile: () => true }
    ]),
    stat: jest.fn().mockImplementation((filePath) => {
      if (filePath.includes('old-file')) {
        return Promise.resolve({ 
          mtime: new Date(Date.now() - 48 * 60 * 60 * 1000), // 48 hours ago
          size: 1024 
        });
      }
      return Promise.resolve({ 
        mtime: new Date(), // Recent file
        size: 2048 
      });
    }),
    unlink: jest.fn().mockResolvedValue(true),
    mkdir: jest.fn().mockResolvedValue(true),
    rename: jest.fn().mockResolvedValue(true)
  }
}));

describe('Job Processors Integration Tests', () => {
  let mockJob;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock job object
    mockJob = {
      id: 'test-job-123',
      data: {},
      progress: jest.fn()
    };
  });

  describe('Data Ingestion Processors', () => {
    test('should process CSV ingestion job', async () => {
      const DataIngestionService = require('../../src/services/DataIngestionService');
      const mockService = {
        ingestFromCSV: jest.fn().mockResolvedValue({
          recordCount: 5,
          recordIds: ['id1', 'id2', 'id3', 'id4', 'id5'],
          skipped: 0
        })
      };
      DataIngestionService.mockImplementation(() => mockService);

      mockJob.data = {
        filePath: '/test/file.csv',
        options: {
          source: 'test-csv',
          entityType: 'pharmacy'
        }
      };

      const result = await dataIngestionProcessors.processCSVIngestion(mockJob);

      expect(result.success).toBe(true);
      expect(result.recordCount).toBe(5);
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });

    test('should process JSON ingestion job', async () => {
      const DataIngestionService = require('../../src/services/DataIngestionService');
      const mockService = {
        ingestFromJSON: jest.fn().mockResolvedValue({
          recordCount: 3,
          recordIds: ['id1', 'id2', 'id3'],
          skipped: 1
        })
      };
      DataIngestionService.mockImplementation(() => mockService);

      mockJob.data = {
        data: [{ name: 'Test' }, { name: 'Test2' }],
        options: {
          source: 'test-json',
          entityType: 'pharmacy'
        }
      };

      const result = await dataIngestionProcessors.processJSONIngestion(mockJob);

      expect(result.success).toBe(true);
      expect(result.recordCount).toBe(3);
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });

    test('should process single record ingestion', async () => {
      const DataIngestionService = require('../../src/services/DataIngestionService');
      const mockRecord = { _id: 'record-123', name: 'Test Pharmacy' };
      const mockService = {
        ingestRecord: jest.fn().mockResolvedValue(mockRecord)
      };
      DataIngestionService.mockImplementation(() => mockService);

      mockJob.data = {
        recordData: { name: 'Test Pharmacy' },
        options: {
          source: 'test-api',
          entityType: 'pharmacy'
        }
      };

      const result = await dataIngestionProcessors.processSingleRecord(mockJob);

      expect(result.success).toBe(true);
      expect(result.recordId).toBe('record-123');
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });
  });

  describe('Preprocessing Processors', () => {
    test('should process record normalization', async () => {
      const mockRecord = {
        _id: 'record-123',
        originalData: { name: 'Test Pharmacy' },
        save: jest.fn().mockResolvedValue(true)
      };
      Record.findById = jest.fn().mockResolvedValue(mockRecord);

      const PreprocessingService = require('../../src/services/PreprocessingService');
      const mockService = {
        normalizeRecord: jest.fn().mockResolvedValue({
          name: 'test pharmacy',
          nameTokens: ['test', 'pharmacy']
        })
      };
      PreprocessingService.mockImplementation(() => mockService);

      mockJob.data = {
        recordId: 'record-123',
        entityType: 'pharmacy'
      };

      const result = await preprocessingProcessors.processRecordNormalization(mockJob);

      expect(result.success).toBe(true);
      expect(result.recordId).toBe('record-123');
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });

    test('should process batch normalization', async () => {
      const mockRecords = [
        { _id: 'record-1', originalData: { name: 'Pharmacy 1' }, save: jest.fn() },
        { _id: 'record-2', originalData: { name: 'Pharmacy 2' }, save: jest.fn() }
      ];
      Record.find = jest.fn().mockResolvedValue(mockRecords);

      const PreprocessingService = require('../../src/services/PreprocessingService');
      const mockService = {
        normalizeRecord: jest.fn().mockResolvedValue({
          name: 'normalized',
          nameTokens: ['normalized']
        })
      };
      PreprocessingService.mockImplementation(() => mockService);

      mockJob.data = {
        recordIds: ['record-1', 'record-2'],
        entityType: 'pharmacy',
        batchSize: 10
      };

      const result = await preprocessingProcessors.processBatchNormalization(mockJob);

      expect(result.success).toBe(true);
      expect(result.processed).toBe(2);
      expect(mockJob.progress).toHaveBeenCalled();
    });
  });

  describe('Similarity Processors', () => {
    test('should process find matches', async () => {
      const mockRecord = {
        _id: 'record-123',
        normalizedData: { name: 'test pharmacy' },
        save: jest.fn().mockResolvedValue(true)
      };
      Record.findById = jest.fn().mockResolvedValue(mockRecord);

      const SimilarityService = require('../../src/services/SimilarityService');
      const mockService = {
        findMatches: jest.fn().mockResolvedValue([
          { recordId: 'match-1', score: 0.85, confidence: 0.9 },
          { recordId: 'match-2', score: 0.75, confidence: 0.8 }
        ])
      };
      SimilarityService.mockImplementation(() => mockService);

      mockJob.data = {
        recordId: 'record-123',
        entityType: 'pharmacy',
        threshold: 0.7
      };

      const result = await similarityProcessors.processFindMatches(mockJob);

      expect(result.success).toBe(true);
      expect(result.matches).toBe(2);
      expect(result.highConfidenceMatches).toBe(1);
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });

    test('should process batch matching', async () => {
      const mockRecords = [
        { _id: 'record-1', normalizedData: { name: 'pharmacy 1' }, save: jest.fn() },
        { _id: 'record-2', normalizedData: { name: 'pharmacy 2' }, save: jest.fn() }
      ];
      Record.find = jest.fn().mockResolvedValue(mockRecords);

      const SimilarityService = require('../../src/services/SimilarityService');
      const mockService = {
        findMatches: jest.fn().mockResolvedValue([
          { recordId: 'match-1', score: 0.85, confidence: 0.9 }
        ])
      };
      SimilarityService.mockImplementation(() => mockService);

      mockJob.data = {
        recordIds: ['record-1', 'record-2'],
        entityType: 'pharmacy',
        threshold: 0.8,
        batchSize: 10
      };

      const result = await similarityProcessors.processBatchMatching(mockJob);

      expect(result.success).toBe(true);
      expect(result.processed).toBe(2);
      expect(mockJob.progress).toHaveBeenCalled();
    });
  });

  describe('Unification Processors', () => {
    test('should process entity unification', async () => {
      const mockRecord = {
        _id: 'record-123',
        originalData: { name: 'Test Pharmacy' }
      };
      const mockMatchedRecords = [
        { _id: 'match-1', originalData: { name: 'Test Pharmacy 2' } }
      ];
      
      Record.findById = jest.fn().mockResolvedValue(mockRecord);
      Record.find = jest.fn().mockResolvedValue(mockMatchedRecords);

      const UnificationService = require('../../src/services/UnificationService');
      const mockUnifiedEntity = { _id: 'unified-123' };
      const mockService = {
        unifyRecords: jest.fn().mockResolvedValue(mockUnifiedEntity)
      };
      UnificationService.mockImplementation(() => mockService);

      mockJob.data = {
        recordId: 'record-123',
        matches: [{ recordId: 'match-1', confidence: 0.95 }],
        entityType: 'pharmacy'
      };

      const result = await unificationProcessors.processEntityUnification(mockJob);

      expect(result.success).toBe(true);
      expect(result.unifiedEntityId).toBe('unified-123');
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });
  });

  describe('Cleanup Processors', () => {
    test('should process temp files cleanup', async () => {
      mockJob.data = {
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      };

      const result = await cleanupProcessors.cleanupTempFiles(mockJob);

      expect(result.success).toBe(true);
      expect(result.filesDeleted).toBeGreaterThanOrEqual(0);
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });

    test('should process log archiving', async () => {
      mockJob.data = {
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      };

      const result = await cleanupProcessors.archiveOldLogs(mockJob);

      expect(result.success).toBe(true);
      expect(result.logsArchived).toBeGreaterThanOrEqual(0);
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });

    test('should process database optimization', async () => {
      Record.deleteMany = jest.fn().mockResolvedValue({ deletedCount: 5 });
      Record.find = jest.fn().mockResolvedValue([]);
      UnifiedEntity.find = jest.fn().mockResolvedValue([]);
      UnifiedEntity.findByIdAndDelete = jest.fn().mockResolvedValue(true);

      const result = await cleanupProcessors.optimizeDatabase(mockJob);

      expect(result.success).toBe(true);
      expect(result.mongoOptimizations).toBeDefined();
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });
  });

  describe('Batch Processors', () => {
    test('should process dataset', async () => {
      Record.countDocuments = jest.fn().mockResolvedValue(100);
      Record.find = jest.fn().mockResolvedValue([
        { _id: 'record-1', processingStatus: 'pending' },
        { _id: 'record-2', processingStatus: 'normalized' }
      ]);

      mockJob.data = {
        source: 'test-source',
        entityType: 'pharmacy',
        batchSize: 50
      };

      const result = await batchProcessors.processDataset(mockJob);

      expect(result.success).toBe(true);
      expect(result.totalRecords).toBe(100);
      expect(mockJob.progress).toHaveBeenCalled();
    });

    test('should reprocess failed jobs', async () => {
      Record.find = jest.fn().mockResolvedValue([
        { _id: 'failed-1', processingStatus: 'failed' },
        { _id: 'failed-2', processingStatus: 'match_failed' }
      ]);
      Record.updateMany = jest.fn().mockResolvedValue({ modifiedCount: 2 });

      mockJob.data = {
        source: 'test-source',
        entityType: 'pharmacy',
        failureTypes: ['failed', 'match_failed'],
        maxAge: 24 * 60 * 60 * 1000
      };

      const result = await batchProcessors.reprocessFailedJobs(mockJob);

      expect(result.success).toBe(true);
      expect(result.totalRecords).toBe(2);
      expect(mockJob.progress).toHaveBeenCalledWith(100);
    });
  });
});
